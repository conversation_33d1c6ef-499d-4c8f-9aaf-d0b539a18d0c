import * as React from 'react';
import {
  TEAMS_CHATS_QUEUE_STORE,
  TEAMS_CHATS_STORE,
  TeamsChatsQueueType,
  DbProvider,
  IRepositoryTeamsChatsQueue,
  ITeamsChatsItem,
} from '../../types/IGeraniumAttaneDB';
import { sortArrayByKey } from '../../utilities/array';

// エラー定数
export const UseTeamsChatsRepositoryError = {
  MAX_TEAMS_CHATS: 'MAX_TEAMS_CHATS',
  NO_OPENDB: 'NO_OPENDB',
  TRANSACTION_PENDING: 'TRANSACTION_PENDING',
};

// 最大TeamsChats件数
export const MAX_TEAMS_CHATS_COUNT = 10;

// 型定義
export type RetrieveTeamsChats = () => Promise<ITeamsChatsItem[]>;
export type ReplaceTeamsChats = (items: ITeamsChatsItem[]) => Promise<void>;
export type AddTeamsChats = (item: ITeamsChatsItem) => Promise<string>;
export type DeleteTeamsChats = (id: string) => Promise<void>;
export type AddTeamsChatsQueue = (data: ITeamsChatsItem,
                                  type: TeamsChatsQueueType) => Promise<void>;
export type DeleteTeamsChatsQueue = (id: string) => Promise<void>;
export type GetTeamsChatsQueues = () => Promise<IRepositoryTeamsChatsQueue[]>;

export type UseTeamsChatsRepositoryReturnType = {
  isInitialized: boolean;
  isTransactionPending: boolean;
  allTeamsChats: ITeamsChatsItem[];
  retrieveTeamsChats: RetrieveTeamsChats | undefined;
  replaceTeamsChats: ReplaceTeamsChats | undefined;
  addTeamsChats: AddTeamsChats | undefined;
  deleteTeamsChats: DeleteTeamsChats | undefined;
  addTeamsChatsQueue: AddTeamsChatsQueue | undefined;
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue | undefined;
  getTeamsChatsQueues: GetTeamsChatsQueues | undefined;
};

/**
 * TeamsChatsアイテムを作成する
 */
const createNewTeamsChatsReposEntry = (item: ITeamsChatsItem): ITeamsChatsItem => ({
  ...item,
  // 必要に応じて追加のプロパティを設定
});

/**
 * TeamsChatsのIndexedDBアクセサー
 */
const useTeamsChatsRepositoryAccessor = (
  openDB?: DbProvider,
): UseTeamsChatsRepositoryReturnType => {
  const isUnmounted = React.useRef(false);

  // リポジトリの初期化後にtrue
  const [isInitialized, setIsInitialized] = React.useState(false);
  // トランザクション処理中はtrue
  const [isTransactionPending, setIsTransactionPending] = React.useState(false);
  // TeamsChats一覧
  const [allTeamsChats, setAllTeamsChats] = React.useState<ITeamsChatsItem[]>([]);

  /**
   * 指定したStoreのデータを全て取得する
   */
  const retrieveTeamsChats: RetrieveTeamsChats = React.useCallback(async () => {
    if (!openDB) return [];
    const db = await openDB();
    try {
      return db
        .getAll<typeof TEAMS_CHATS_STORE>(TEAMS_CHATS_STORE)
        .then((results) => sortArrayByKey(results, 'countId', 'asc'));
    } finally {
      db.close();
    }
  }, [openDB]);

  /**
   * IndexedDBの内容で画面用データを再生成する
   */
  const renewAllTeamsChats = React.useCallback(async () => {
    const teamsChats = await retrieveTeamsChats().catch(() => []);
    if (isUnmounted.current) return;
    setAllTeamsChats(teamsChats);
  }, [retrieveTeamsChats]);

  /**
   * 初期化処理
   */
  React.useEffect(() => {
    if (!openDB) return;
    (async () => {
      // TeamsChatsを全件取得して保存
      await renewAllTeamsChats();
      setIsInitialized(true);
    })();
  }, [openDB, renewAllTeamsChats]);

  React.useEffect(() => () => { isUnmounted.current = true; }, []);

  /**
   * TeamsChatsを全件置き換える
   * itemsが0件の場合は全消去を行う
   */
  const replaceTeamsChats: ReplaceTeamsChats = React.useCallback(
    async (items: ITeamsChatsItem[]) => {
      if (!openDB) return Promise.reject(new Error('no opendb'));

      setIsTransactionPending(true);
      const db = await openDB();

      try {
        const tx = db.transaction(TEAMS_CHATS_STORE, 'readwrite');
        const teamsChatsStore = tx.objectStore(TEAMS_CHATS_STORE);

        // トランザクションの内容
        const operations = [
          // 全件削除
          teamsChatsStore.clear(),
          // データ登録
          items.map((item) => teamsChatsStore.put(item, item.id)),
          tx.done,
        ];

        return Promise.all(operations).then(() => Promise.resolve());

      } finally {
        db.close();
        await renewAllTeamsChats();
        setIsTransactionPending(false);
      }
    }, [openDB, renewAllTeamsChats],
  );

  /**
   * TeamsChatsを一件書き込む
   * 最大件数以上に登録しようとしたらrejectする
   * @return resolve時に書込み成功したitemのIDを返却する
   */
  const addTeamsChats: AddTeamsChats = React.useCallback(async (item: ITeamsChatsItem) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    if (allTeamsChats.length >= MAX_TEAMS_CHATS_COUNT) {
      // 既にMAX_TEAMS_CHATS_COUNTの件数分登録があった場合はreject
      return Promise.reject(UseTeamsChatsRepositoryError.MAX_TEAMS_CHATS);
    }

    setIsTransactionPending(true);
    const db = await openDB();
    const reposEntry = createNewTeamsChatsReposEntry(item);

    try {
      return db.put<typeof TEAMS_CHATS_STORE>(TEAMS_CHATS_STORE, reposEntry, reposEntry.id);
    } finally {
      db.close();
      setIsTransactionPending(false);
      renewAllTeamsChats();
    }
  }, [openDB, allTeamsChats, renewAllTeamsChats]);

  /**
   * TeamsChatsを一件削除する
   */
  const deleteTeamsChats: DeleteTeamsChats = React.useCallback(async (id: string) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    setIsTransactionPending(true);
    const db = await openDB();

    try {
      return db.delete<typeof TEAMS_CHATS_STORE>(TEAMS_CHATS_STORE, id);
    } finally {
      db.close();
      setIsTransactionPending(false);
      renewAllTeamsChats();
    }
  }, [openDB, renewAllTeamsChats]);

  /**
   * TeamsChatsキューとTeamsChatsを一件追加する
   * TeamsChatsキューとTeamsChatsの両方に影響がある
   */
  const addTeamsChatsQueue: AddTeamsChatsQueue = React.useCallback(
    async (data: ITeamsChatsItem, type: TeamsChatsQueueType) => {
      if (!openDB) return Promise.reject(new Error('no opendb'));

      // 連弾防止のため処理中は次の処理を開始させない
      if (isTransactionPending) return Promise.resolve();

      if (type === 'PUT' && allTeamsChats.length >= MAX_TEAMS_CHATS_COUNT) {
        // 既にMAX_TEAMS_CHATS_COUNTの件数分登録があった場合はreject
        return Promise.reject(UseTeamsChatsRepositoryError.MAX_TEAMS_CHATS);
      }

      // 連弾防止を開始
      setIsTransactionPending(true);

      const db = await openDB();

      try {
        const tx = db.transaction([TEAMS_CHATS_STORE, TEAMS_CHATS_QUEUE_STORE], 'readwrite');
        const teamsChatsStore = tx.objectStore(TEAMS_CHATS_STORE);
        const queueStore = tx.objectStore(TEAMS_CHATS_QUEUE_STORE);

        const currentQueue = await queueStore.get(data.id);

        // 相反するキューが既に存在する場合
        const hasPairedQueueAlready = currentQueue && currentQueue.type !== type;

        await Promise.all([
          hasPairedQueueAlready
            // 相反するキューが存在する場合はそのキューを削除
            ? queueStore.delete(data.id)
            : queueStore.put({ type, data, date: new Date() }, data.id),
          // ローカルTeamsChatsを更新
          type === 'PUT' ? teamsChatsStore.put(data, data.id) : teamsChatsStore.delete(data.id),
          tx.done,
        ]);

        return Promise.resolve();

      } finally {
        db.close();
        setIsTransactionPending(false);
        renewAllTeamsChats();
      }
    }, [openDB, isTransactionPending, allTeamsChats, renewAllTeamsChats],
  );

  /**
   * TeamsChatsキューを一件削除する
   */
  const deleteTeamsChatsQueue: DeleteTeamsChatsQueue = React.useCallback(async (id: string) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    const db = await openDB();

    try {
      return db.delete<typeof TEAMS_CHATS_QUEUE_STORE>(TEAMS_CHATS_QUEUE_STORE, id);
    } finally {
      db.close();
    }
  }, [openDB]);

  /**
   * TeamsChatsキューを全件取得する
   */
  const getTeamsChatsQueues: GetTeamsChatsQueues = React.useCallback(async () => {
    if (!openDB) return [];

    const db = await openDB();

    try {
      return db.getAll<typeof TEAMS_CHATS_QUEUE_STORE>(TEAMS_CHATS_QUEUE_STORE);
    } finally {
      db.close();
    }
  }, [openDB]);

  return {
    isInitialized,
    isTransactionPending,
    allTeamsChats,
    retrieveTeamsChats: openDB ? retrieveTeamsChats : undefined,
    replaceTeamsChats: openDB ? replaceTeamsChats : undefined,
    addTeamsChats: openDB ? addTeamsChats : undefined,
    deleteTeamsChats: openDB ? deleteTeamsChats : undefined,
    addTeamsChatsQueue: openDB ? addTeamsChatsQueue : undefined,
    deleteTeamsChatsQueue: openDB ? deleteTeamsChatsQueue : undefined,
    getTeamsChatsQueues: openDB ? getTeamsChatsQueues : undefined,
  };
};

export default useTeamsChatsRepositoryAccessor;
