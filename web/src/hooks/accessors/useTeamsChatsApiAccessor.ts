import React from 'react';
import { EventReportType, EventReporter } from '@avanade-teams/app-insights-reporter';
import { Reporters } from '../../types/Reporters';
import { fetchUrlRes } from '../../utilities/commonFunction';
import { getUniqueNameByToken } from '../../utilities/token/jwt';
import environment from '../../utilities/environment';

type TokenProvider = (() => Promise<string>) | undefined;

// エラー定数
export const UseTeamsChatsApiAccessorError = {
  MISSING_PARAMS: 'MISSING_PARAMS',
  NO_TOKENS: 'NO_TOKENS',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
};

// API URL プレフィックス
const PREFIX = environment.REACT_APP_API_URL ?? '/api';

/**
 * Teams設定保存に必要な値
 * DBスキーマに合わせた型定義
 */
export interface ITeamsChatsRequest {
  // PartitionKey = userId (自動で設定される)
  // RowKey = chatId (チャットの場合) または channelId (チャネルの場合)
  // 共通フィールド
  countId: number; // ユーザーが選択できるチャット数 (1-10)
  chatType: 'oneOnOne' | 'group' | 'meeting' |'TeamsChannel';

  // チャット用フィールド (chatType === 'チャット' の場合)
  chatId?: string; // RowKeyとしても使用

  // チャネル用フィールド (chatType === 'チャネル' の場合)
  teamId?: string; // チームID
  channelId?: string; // RowKeyとしても使用
}

/**
 * Teams設定取得のレスポンス型
 * バックエンドのTeamsChatsResponseに対応
 */
export interface ITeamsChatsResponse {
  Id: string;
  countId: number;
  chatId?: string;
  channelId?: string;
  teamId?: string;
  chatType: string;
  UserId: string;
  CreatedAt: string;
  UpdatedAt: string;
}

// API 関数の型定義
export type PostTeamsChatsApi = (request: ITeamsChatsRequest) => Promise<void>;
export type DeleteTeamsChatsApi = (chatId: string) => Promise<void>;
export type GetTeamsChatsApi = () => Promise<ITeamsChatsResponse[]>;

export type UseTeamsChatsApiReturnType = {
  postTeamsChatsApi: PostTeamsChatsApi | undefined;
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined;
  getTeamsChatsApi: GetTeamsChatsApi | undefined;
};

/**
 * Teams チャット設定の POST URL を作成する
 * @param userId
 */
export function createPostTeamsChatsUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/teams-chats`;
}

/**
 * Teams チャット設定の GET URL を作成する
 * @param userId
 */
export function createGetTeamsChatsUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/teams-chats`;
}

/**
 * TeamsChatTableへ登録へ行く
 */
async function postTeamsChatsApiImpl(
  tokenProvider: TokenProvider,
  request: ITeamsChatsRequest,
  report: EventReporter,
): Promise<void> {
  if (!tokenProvider) return Promise.reject(new Error(UseTeamsChatsApiAccessorError.NO_TOKENS));

  // パラメータ検証
  if (!request.chatType || typeof request.countId !== 'number') {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  // countIdの範囲チェック (1-10)
  if (request.countId < 1 || request.countId > 10) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  // チャット用フィールドの検証
  if ((request.chatType === 'oneOnOne' || request.chatType === 'group' || request.chatType === 'meeting') && !request.chatId) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  // チャネル用フィールドの検証
  if (request.chatType === 'TeamsChannel' && (!request.channelId || !request.teamId)) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }
  // TODO:userIdを取得
  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  try {
    const res = await fetchUrlRes(
      token,
      'POST',
      createPostTeamsChatsUrl(uId),
      JSON.stringify(request),
    );

    if (res.status !== 201 && res.status !== 200) {
      report({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: new Error(res.statusText),
      });
      throw new Error(`API Error: ${res.status} ${res.statusText}`);
    }
  } catch (e) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: e as Error,
    });
    throw e;
  }

  return Promise.resolve();
}

/**
 * TeamsChats削除API
 */
async function deleteTeamsChatsApiImpl(
  tokenProvider: TokenProvider,
  chatId: string,
  report: EventReporter,
): Promise<void> {
  if (!chatId) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }
  if (!tokenProvider) return Promise.reject(new Error(UseTeamsChatsApiAccessorError.NO_TOKENS));
  // TODO:userIdを取得
  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  try {
    const url = `${PREFIX}/users/${encodeURIComponent(uId)}/teams-chats/${encodeURIComponent(chatId)}`;
    const res = await fetchUrlRes(
      token,
      'DELETE',
      url,
    );

    if (res.status !== 204 && res.status !== 200) {
      report({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: new Error(res.statusText),
      });
      throw new Error(`API Error: ${res.status} ${res.statusText}`);
    }
  } catch (e) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: e as Error,
    });
    throw e;
  }

  return Promise.resolve();
}

/**
 * TeamsChats取得API
 */
async function getTeamsChatsApiImpl(
  tokenProvider: TokenProvider,
  report: EventReporter,
): Promise<ITeamsChatsResponse[]> {
  if (!tokenProvider) return Promise.reject(new Error(UseTeamsChatsApiAccessorError.NO_TOKENS));

  // TODO:userIdを取得
  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  try {
    const res = await fetchUrlRes(
      token,
      'GET',
      createGetTeamsChatsUrl(uId),
    );

    if (res.status !== 200) {
      report({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: new Error(res.statusText),
      });
      throw new Error(`API Error: ${res.status} ${res.statusText}`);
    }

    const data = await res.json();
    return data as ITeamsChatsResponse[];
  } catch (e) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: e as Error,
    });
    throw e;
  }
}

/**
 * TeamsChatTableへ接続するアクセサー
 * @param tokenProvider
 * @param reporters
 */
const useTeamsChatsApiAccessor = (
  tokenProvider: TokenProvider,
  reporters: Reporters,
): UseTeamsChatsApiReturnType => {
  const [report] = reporters;

  const postTeamsChatsApi: PostTeamsChatsApi = React.useCallback(
    async (request: ITeamsChatsRequest) => postTeamsChatsApiImpl(
      tokenProvider, request, report,
    ), [tokenProvider, report],
  );

  const deleteTeamsChatsApi: DeleteTeamsChatsApi = React.useCallback(
    async (chatId: string) => deleteTeamsChatsApiImpl(
      tokenProvider, chatId, report,
    ), [tokenProvider, report],
  );

  const getTeamsChatsApi: GetTeamsChatsApi = React.useCallback(
    async () => getTeamsChatsApiImpl(
      tokenProvider, report,
    ), [tokenProvider, report],
  );

  return {
    postTeamsChatsApi: tokenProvider ? postTeamsChatsApi : undefined,
    deleteTeamsChatsApi: tokenProvider ? deleteTeamsChatsApi : undefined,
    getTeamsChatsApi: tokenProvider ? getTeamsChatsApi : undefined,
  };
};

export default useTeamsChatsApiAccessor;
