import '@testing-library/jest-dom';
import { EventReportType } from '@avanade-teams/app-insights-reporter';
import {
  convertUserChatItemToTeamsChatsItem,
  convertUserChatItemsToTeamsChatsItems,
  convertTeamsChatsItemToRequest,
  sendTeamsChatsQueues,
  addMultipleTeamsChatsToQueue,
  UseTeamsChatsQueueError,
  ITeamsChatsItem,
  ITeamsChatsQueue,
} from './useTeamsChatsQueue';
import { IUserChatItem } from '../accessors/useUserChatsAndChannelsAccessor';

// テスト用のモックデータ
const createMockUserChatItem = (overrides: Partial<IUserChatItem> = {}): IUserChatItem => ({
  id: 'test-chat-id',
  name: 'テストチャット',
  type: 'チャット',
  chatType: 'oneOnOne',
  ...overrides,
});

const createMockTeamsChatsItem = (overrides: Partial<ITeamsChatsItem> = {}): ITeamsChatsItem => ({
  id: 'test-chat-id',
  name: 'テストチャット',
  type: 'チャット',
  chatType: 'oneOnOne',
  countId: 1,
  ...overrides,
});

const createMockEventReporter = () => {
  const mockReporter = jest.fn();
  return mockReporter;
};

describe('useTeamsChatsQueue', () => {
  describe('convertUserChatItemToTeamsChatsItem', () => {
    it('IUserChatItemをITeamsChatsItemに正しく変換する', () => {
      const userChatItem = createMockUserChatItem({
        id: 'chat-123',
        name: 'テストチャット',
        type: 'チャット',
        chatType: 'group',
      });

      const result = convertUserChatItemToTeamsChatsItem(userChatItem, 2);

      expect(result).toEqual({
        id: 'chat-123',
        name: 'テストチャット',
        type: 'チャット',
        chatType: 'group',
        teamId: undefined,
        countId: 2,
      });
    });

    it('チャネルアイテムを正しく変換する', () => {
      const userChatItem = createMockUserChatItem({
        id: 'channel-456',
        name: 'テストチャネル',
        type: 'チャネル',
        chatType: 'TeamsChannel',
        teamId: 'team-789',
      });

      const result = convertUserChatItemToTeamsChatsItem(userChatItem, 3);

      expect(result).toEqual({
        id: 'channel-456',
        name: 'テストチャネル',
        type: 'チャネル',
        chatType: 'TeamsChannel',
        teamId: 'team-789',
        countId: 3,
      });
    });
  });

  describe('convertUserChatItemsToTeamsChatsItems', () => {
    it('複数のIUserChatItemを正しく変換し、countIdを連番で設定する', () => {
      const userChatItems = [
        createMockUserChatItem({ id: 'chat-1', name: 'チャット1' }),
        createMockUserChatItem({ id: 'chat-2', name: 'チャット2' }),
        createMockUserChatItem({ id: 'chat-3', name: 'チャット3' }),
      ];

      const result = convertUserChatItemsToTeamsChatsItems(userChatItems);

      expect(result).toHaveLength(3);
      expect(result[0].countId).toBe(1);
      expect(result[1].countId).toBe(2);
      expect(result[2].countId).toBe(3);
      expect(result[0].id).toBe('chat-1');
      expect(result[1].id).toBe('chat-2');
      expect(result[2].id).toBe('chat-3');
    });
  });

  describe('convertTeamsChatsItemToRequest', () => {
    it('チャットアイテムをAPIリクエストに正しく変換する', () => {
      const teamsChatsItem = createMockTeamsChatsItem({
        id: 'chat-123',
        type: 'チャット',
        chatType: 'oneOnOne',
        countId: 1,
      });

      const result = convertTeamsChatsItemToRequest(teamsChatsItem);

      expect(result).toEqual({
        countId: 1,
        chatType: 'oneOnOne',
        chatId: 'chat-123',
      });
    });

    it('チャネルアイテムをAPIリクエストに正しく変換する', () => {
      const teamsChatsItem = createMockTeamsChatsItem({
        id: 'channel-456',
        type: 'チャネル',
        chatType: 'TeamsChannel',
        teamId: 'team-789',
        countId: 2,
      });

      const result = convertTeamsChatsItemToRequest(teamsChatsItem);

      expect(result).toEqual({
        countId: 2,
        chatType: 'TeamsChannel',
        teamId: 'team-789',
        channelId: 'channel-456',
      });
    });
  });

  describe('sendTeamsChatsQueues', () => {
    it('空のキューの場合は何もしない', async () => {
      const mockPostApi = jest.fn();
      const mockEventReporter = createMockEventReporter();

      const result = await sendTeamsChatsQueues([], mockPostApi, mockEventReporter);

      expect(result).toEqual([]);
      expect(mockPostApi).not.toHaveBeenCalled();
      expect(mockEventReporter).not.toHaveBeenCalled();
    });

    it('成功時は全てのキューを処理して空配列を返す', async () => {
      const mockPostApi = jest.fn().mockResolvedValue(undefined);
      const mockEventReporter = createMockEventReporter();

      const queues: ITeamsChatsQueue[] = [
        {
          date: new Date(),
          data: createMockTeamsChatsItem({ id: 'chat-1', countId: 1 }),
        },
        {
          date: new Date(),
          data: createMockTeamsChatsItem({ id: 'chat-2', countId: 2 }),
        },
      ];

      const result = await sendTeamsChatsQueues(queues, mockPostApi, mockEventReporter);

      expect(result).toEqual([]);
      expect(mockPostApi).toHaveBeenCalledTimes(2);
      expect(mockEventReporter).not.toHaveBeenCalled();
    });

    it('エラー時は残りのキューを返し、エラーレポートを送信する', async () => {
      const mockPostApi = jest.fn()
        .mockResolvedValueOnce(undefined) // 1回目は成功
        .mockRejectedValueOnce(new Error('API Error')); // 2回目は失敗
      const mockEventReporter = createMockEventReporter();

      const queues: ITeamsChatsQueue[] = [
        {
          date: new Date(),
          data: createMockTeamsChatsItem({ id: 'chat-1', countId: 1 }),
        },
        {
          date: new Date(),
          data: createMockTeamsChatsItem({ id: 'chat-2', countId: 2 }),
        },
        {
          date: new Date(),
          data: createMockTeamsChatsItem({ id: 'chat-3', countId: 3 }),
        },
      ];

      const result = await sendTeamsChatsQueues(queues, mockPostApi, mockEventReporter);

      expect(result).toHaveLength(2); // 残り2つのキュー
      expect(result[0].data.id).toBe('chat-2');
      expect(result[1].data.id).toBe('chat-3');
      expect(mockPostApi).toHaveBeenCalledTimes(2);
      expect(mockEventReporter).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsQueueError.QUEUE_SEND_ERROR,
        error: expect.any(Error),
        customProperties: {
          queueLength: 2,
          currentQueueId: 'chat-2',
          processedCount: 2,
        },
      });
    });
  });

  describe('addMultipleTeamsChatsToQueue', () => {
    it('空のアイテム配列の場合は何もしない', async () => {
      const mockPostApi = jest.fn();
      const mockEventReporter = createMockEventReporter();

      await addMultipleTeamsChatsToQueue([], mockPostApi, mockEventReporter);

      expect(mockPostApi).not.toHaveBeenCalled();
      expect(mockEventReporter).not.toHaveBeenCalled();
    });

    it('postTeamsChatsApiがundefinedの場合は何もしない', async () => {
      const mockEventReporter = createMockEventReporter();
      const items = [createMockTeamsChatsItem()];

      await addMultipleTeamsChatsToQueue(items, undefined, mockEventReporter);

      expect(mockEventReporter).not.toHaveBeenCalled();
    });

    it('全て成功した場合はエラーを投げない', async () => {
      const mockPostApi = jest.fn().mockResolvedValue(undefined);
      const mockEventReporter = createMockEventReporter();
      const items = [
        createMockTeamsChatsItem({ id: 'chat-1', countId: 1 }),
        createMockTeamsChatsItem({ id: 'chat-2', countId: 2 }),
      ];

      await expect(addMultipleTeamsChatsToQueue(items, mockPostApi, mockEventReporter))
        .resolves.toBeUndefined();

      expect(mockPostApi).toHaveBeenCalledTimes(2);
      expect(mockEventReporter).not.toHaveBeenCalled();
    });

    it('一部失敗した場合はエラーを投げる', async () => {
      const mockPostApi = jest.fn()
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('API Error'));
      const mockEventReporter = createMockEventReporter();
      const items = [
        createMockTeamsChatsItem({ id: 'chat-1', countId: 1 }),
        createMockTeamsChatsItem({ id: 'chat-2', countId: 2 }),
      ];

      await expect(addMultipleTeamsChatsToQueue(items, mockPostApi, mockEventReporter))
        .rejects.toThrow('1 items failed to save');

      expect(mockPostApi).toHaveBeenCalledTimes(2);
      expect(mockEventReporter).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsQueueError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: expect.any(Error),
        customProperties: {
          totalItems: 2,
        },
      });
    });
  });
});
