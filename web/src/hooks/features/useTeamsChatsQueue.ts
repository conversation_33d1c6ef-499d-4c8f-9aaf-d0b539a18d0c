import * as React from 'react';
import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import { IUserChatItem } from '../accessors/useUserChatsAndChannelsAccessor';
import { PostTeamsChatsApi, ITeamsChatsRequest } from '../accessors/useTeamsChatsApiAccessor';
import { ITeamsChatsItem, IRepositoryTeamsChatsQueue } from '../../types/IGeraniumAttaneDB';

// 型定義はIGeraniumAttaneDBから使用

// エラー定数
export const UseTeamsChatsQueueError = {
  MISSING_PARAMS: 'MISSING_PARAMS',
  NO_TOKENS: 'NO_TOKENS',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
  QUEUE_SEND_ERROR: 'QUEUE_SEND_ERROR',
};

/**
 * IUserChatItemからITeamsChatsItemに変換する
 */
export function convertUserChatItemToTeamsChatsItem(
  item: IUserChatItem,
  countId: number,
): ITeamsChatsItem {
  return {
    id: item.id,
    name: item.name,
    type: item.type,
    chatType: item.chatType,
    teamId: item.teamId,
    countId,
  };
}

/**
 * 複数のIUserChatItemからITeamsChatsItemの配列に変換する
 */
export function convertUserChatItemsToTeamsChatsItems(
  items: IUserChatItem[],
): ITeamsChatsItem[] {
  return items.map((item, index) => convertUserChatItemToTeamsChatsItem(item, index + 1));
}

/**
 * ITeamsChatsItemからITeamsChatsRequestに変換する
 */
export function convertTeamsChatsItemToRequest(item: ITeamsChatsItem): ITeamsChatsRequest {
  return {
    countId: item.countId,
    chatType: item.chatType,
    ...(item.type === 'チャット' && {
      chatId: item.id,
    }),
    ...(item.type === 'チャネル' && {
      teamId: item.teamId,
      channelId: item.id,
    }),
  };
}

/**
 * キューを直列で逐次送信し、成功したキューは削除する
 * 途中で失敗した場合はキューを保持し、送信を止める
 */
export async function sendTeamsChatsQueues(
  queues: IRepositoryTeamsChatsQueue[],
  postTeamsChatsApi: PostTeamsChatsApi,
  eventReporter: EventReporter,
): Promise<IRepositoryTeamsChatsQueue[]> {
  if (!queues || queues.length === 0) {
    return [];
  }

  const remainingQueues: IRepositoryTeamsChatsQueue[] = [...queues];
  let processedCount = 0;

  // eslint-disable-next-line no-await-in-loop
  while (remainingQueues.length > 0 && processedCount < queues.length) {
    const queue = remainingQueues[0];
    processedCount += 1;

    try {
      const request = convertTeamsChatsItemToRequest(queue.data);
      // eslint-disable-next-line no-await-in-loop
      await postTeamsChatsApi(request);

      // 成功したキューを削除
      remainingQueues.shift();

    } catch (e) {
      // エラーが発生した場合は処理を停止
      eventReporter({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsQueueError.QUEUE_SEND_ERROR,
        error: e as Error,
        customProperties: {
          queueLength: remainingQueues.length,
          currentQueueId: queue.data.id,
          processedCount,
        },
      });
      break;
    }
  }

  return remainingQueues;
}

/**
 * 複数のTeamsChatsアイテムをキューに追加してサーバーに送信する
 */
export async function addMultipleTeamsChatsToQueue(
  items: ITeamsChatsItem[],
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  eventReporter: EventReporter,
): Promise<void> {
  if (!postTeamsChatsApi || items.length === 0) {
    return Promise.resolve();
  }

  // キューを作成
  const queues: IRepositoryTeamsChatsQueue[] = items.map((item) => ({
    date: new Date(),
    type: 'PUT',
    data: item,
  }));

  try {
    // キューを送信
    const remainingQueues = await sendTeamsChatsQueues(queues, postTeamsChatsApi, eventReporter);

    if (remainingQueues.length > 0) {
      // 未送信のキューがある場合はエラーとして扱う
      throw new Error(`${remainingQueues.length} items failed to save`);
    }

    return Promise.resolve();
  } catch (e) {
    eventReporter({
      type: EventReportType.SYS_ERROR,
      name: UseTeamsChatsQueueError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: e as Error,
      customProperties: {
        totalItems: items.length,
      },
    });
    throw e;
  }
}

export type UseTeamsChatsQueueReturnType = {
  addMultipleTeamsChats: ((items: ITeamsChatsItem[]) => Promise<void>) | undefined;
  convertUserChatItems: (items: IUserChatItem[]) => ITeamsChatsItem[];
};

/**
 * TeamsChatsのキュー機能を提供するカスタムフック
 */
const useTeamsChatsQueue = (
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  eventReporter: EventReporter,
): UseTeamsChatsQueueReturnType => {

  const addMultipleTeamsChats = React.useCallback(
    async (items: ITeamsChatsItem[]) => addMultipleTeamsChatsToQueue(
      items,
      postTeamsChatsApi,
      eventReporter,
    ),
    [postTeamsChatsApi, eventReporter],
  );

  const convertUserChatItems = React.useCallback(
    (items: IUserChatItem[]) => convertUserChatItemsToTeamsChatsItems(items),
    [],
  );

  return {
    addMultipleTeamsChats: postTeamsChatsApi ? addMultipleTeamsChats : undefined,
    convertUserChatItems,
  };
};

export default useTeamsChatsQueue;
