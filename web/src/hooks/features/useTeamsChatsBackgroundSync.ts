import * as React from 'react';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import { UseTeamsChatsRepositoryReturnType } from '../accessors/useTeamsChatsRepositoryAccessor';
import useTeamsChatsApiAccessor from '../accessors/useTeamsChatsApiAccessor';
import { sendTeamsChatsQueues } from './useRemoteTeamsChatsFeature';

// バックグラウンド同期の設定
const BACKGROUND_SYNC_INTERVAL = 30000; // 30秒
const NETWORK_CHECK_INTERVAL = 5000; // 5秒
const MAX_RETRY_ATTEMPTS = 3;

export type UseTeamsChatsBackgroundSyncReturnType = {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  queueCount: number;
  startBackgroundSync: () => void;
  stopBackgroundSync: () => void;
  forceSyncNow: () => Promise<void>;
};

/**
 * TeamsChatsのバックグラウンド同期機能
 */
const useTeamsChatsBackgroundSync = (
  useRepositoryReturn: UseTeamsChatsRepositoryReturnType,
  useTeamsChatsApiReturn: ReturnType<typeof useTeamsChatsApiAccessor>,
  eventReporter: EventReporter,
): UseTeamsChatsBackgroundSyncReturnType => {
  // 状態管理
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);
  const [isSyncing, setIsSyncing] = React.useState(false);
  const [lastSyncTime, setLastSyncTime] = React.useState<Date | null>(null);
  const [queueCount, setQueueCount] = React.useState(0);

  // タイマーの参照
  const syncIntervalRef = React.useRef<NodeJS.Timeout | null>(null);
  const networkCheckIntervalRef = React.useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = React.useRef(0);

  // リポジトリとAPIアクセサーから必要な関数を取得
  const { getTeamsChatsQueues, deleteTeamsChatsQueue } = useRepositoryReturn;
  const { postTeamsChatsApi, deleteTeamsChatsApi } = useTeamsChatsApiReturn;

  /**
   * キューの件数を更新する
   */
  const updateQueueCount = React.useCallback(async () => {
    if (!getTeamsChatsQueues) return;
    try {
      const queues = await getTeamsChatsQueues();
      setQueueCount(queues.length);
    } catch (error) {
      console.error('Failed to get queue count:', error);
    }
  }, [getTeamsChatsQueues]);

  /**
   * ネットワーク状態の監視
   */
  React.useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      retryCountRef.current = 0; // リトライカウントをリセット
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  /**
   * 同期処理の実行
   */
  const performSync = React.useCallback(async (): Promise<boolean> => {
    if (!getTeamsChatsQueues
       || !postTeamsChatsApi
       || !deleteTeamsChatsApi
       || !deleteTeamsChatsQueue) {
      return false;
    }

    if (isSyncing) {
      return false; // 既に同期中の場合はスキップ
    }

    setIsSyncing(true);

    try {
      const queues = await getTeamsChatsQueues();

      if (queues.length === 0) {
        setLastSyncTime(new Date());
        return true; // 同期するものがない場合は成功とみなす
      }

      // キューを送信
      await sendTeamsChatsQueues(
        queues,
        postTeamsChatsApi,
        deleteTeamsChatsApi,
        deleteTeamsChatsQueue,
        eventReporter,
      );

      // 同期成功
      setLastSyncTime(new Date());
      retryCountRef.current = 0;
      await updateQueueCount();

      return true;

    } catch (error) {
      console.error('Background sync failed:', error);
      retryCountRef.current += 1;

      // 最大リトライ回数に達した場合はオフライン状態とみなす
      if (retryCountRef.current >= MAX_RETRY_ATTEMPTS) {
        setIsOnline(false);
      }

      return false;
    } finally {
      setIsSyncing(false);
    }
  }, [
    getTeamsChatsQueues,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
    isSyncing,
    updateQueueCount,
  ]);

  /**
   * バックグラウンド同期の開始
   */
  const startBackgroundSync = React.useCallback(() => {
    // 既存のタイマーをクリア
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current);
    }
    if (networkCheckIntervalRef.current) {
      clearInterval(networkCheckIntervalRef.current);
    }

    // 定期同期タイマーを開始
    syncIntervalRef.current = setInterval(async () => {
      if (isOnline) {
        await performSync();
      }
    }, BACKGROUND_SYNC_INTERVAL);

    // ネットワーク復旧チェックタイマーを開始
    networkCheckIntervalRef.current = setInterval(async () => {
      if (!isOnline && navigator.onLine) {
        // ネットワークが復旧した場合は即座に同期を試行
        const success = await performSync();
        if (success) {
          setIsOnline(true);
        }
      }
    }, NETWORK_CHECK_INTERVAL);

    // 初回同期を実行
    if (isOnline) {
      performSync();
    }
  }, [isOnline, performSync]);

  /**
   * バックグラウンド同期の停止
   */
  const stopBackgroundSync = React.useCallback(() => {
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current);
      syncIntervalRef.current = null;
    }
    if (networkCheckIntervalRef.current) {
      clearInterval(networkCheckIntervalRef.current);
      networkCheckIntervalRef.current = null;
    }
  }, []);

  /**
   * 手動での即座同期
   */
  const forceSyncNow = React.useCallback(async () => {
    await performSync();
    await updateQueueCount();
  }, [performSync, updateQueueCount]);

  // コンポーネントのマウント時にキュー件数を取得
  React.useEffect(() => {
    updateQueueCount();
  }, [updateQueueCount]);

  // コンポーネントのアンマウント時にタイマーをクリア
  React.useEffect(() => () => {
    stopBackgroundSync();
  }, [stopBackgroundSync]);

  return {
    isOnline,
    isSyncing,
    lastSyncTime,
    queueCount,
    startBackgroundSync,
    stopBackgroundSync,
    forceSyncNow,
  };
};

export default useTeamsChatsBackgroundSync;
