import * as React from 'react';
import {
  Spinner, Text, Icon, Stack, MessageBar, MessageBarType,
} from '@fluentui/react';
import dayjs from 'dayjs';

export interface TeamsChatsStatusIndicatorProps {
  /** オンライン状態 */
  isOnline: boolean;
  /** 同期中かどうか */
  isSyncing: boolean;
  /** トランザクション処理中かどうか */
  isTransactionPending: boolean;
  /** 最後の同期時刻 */
  lastSyncTime: Date | null;
  /** 未送信キューの件数 */
  queueCount: number;
  /** 最後のエラー */
  lastError: Error | null;
  /** エラーをクリアする関数 */
  onClearError?: () => void;
  /** 手動同期を実行する関数 */
  onForcSync?: () => Promise<void>;
  /** コンパクト表示モード */
  compact?: boolean;
}

/**
 * TeamsChats機能の状態を表示するインジケーター
 */
const TeamsChatsStatusIndicator: React.FC<TeamsChatsStatusIndicatorProps> = ({
  isOnline,
  isSyncing,
  isTransactionPending,
  lastSyncTime,
  queueCount,
  lastError,
  onClearError,
  onForcSync,
  compact = false,
}) => {
  const [isManualSyncing, setIsManualSyncing] = React.useState(false);

  /**
   * 手動同期の実行
   */
  const handleManualSync = React.useCallback(async () => {
    if (!onForcSync || isManualSyncing) return;

    setIsManualSyncing(true);
    try {
      await onForcSync();
    } catch (error) {
      console.error('Manual sync failed:', error);
    } finally {
      setIsManualSyncing(false);
    }
  }, [onForcSync, isManualSyncing]);

  /**
   * 状態アイコンとテキストを取得
   */
  const getStatusInfo = React.useMemo(() => {
    if (lastError) {
      return {
        icon: 'ErrorBadge',
        color: '#d13438',
        text: 'エラー',
        description: lastError.message,
      };
    }

    if (isSyncing || isTransactionPending || isManualSyncing) {
      return {
        icon: null, // Spinnerを使用
        color: '#0078d4',
        text: '同期中...',
        description: isTransactionPending ? 'ローカル処理中' : 'サーバー同期中',
      };
    }

    if (!isOnline) {
      return {
        icon: 'CloudOffline',
        color: '#ff8c00',
        text: 'オフライン',
        description: queueCount > 0 ? `${queueCount}件の未送信データがあります` : 'ネットワークに接続されていません',
      };
    }

    if (queueCount > 0) {
      return {
        icon: 'CloudUpload',
        color: '#0078d4',
        text: '同期待ち',
        description: `${queueCount}件のデータが同期待ちです`,
      };
    }

    return {
      icon: 'CloudCheck',
      color: '#107c10',
      text: '同期済み',
      description: lastSyncTime ? `最終同期: ${dayjs(lastSyncTime).format('HH:mm:ss')}` : '同期完了',
    };
  }, [lastError,
    isSyncing,
    isTransactionPending,
    isManualSyncing,
    isOnline,
    queueCount,
    lastSyncTime]);

  if (compact) {
    return (
      <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 4 }}>
        {getStatusInfo.icon ? (
          <Icon
            iconName={getStatusInfo.icon}
            style={{ color: getStatusInfo.color, fontSize: 12 }}
          />
        ) : (
          <Spinner size={0} style={{ width: 12, height: 12 }} />
        )}
        <Text variant="small" style={{ color: getStatusInfo.color }}>
          {getStatusInfo.text}
        </Text>
      </Stack>
    );
  }

  return (
    <Stack tokens={{ childrenGap: 8 }}>
      {/* エラー表示 */}
      {lastError && (
        <MessageBar
          messageBarType={MessageBarType.error}
          onDismiss={onClearError}
          dismissButtonAriaLabel="エラーを閉じる"
        >
          <Text variant="small">
            <strong>同期エラー:</strong>
            {' '}
            {lastError.message}
          </Text>
        </MessageBar>
      )}

      {/* 未送信キューの警告 */}
      {!lastError && queueCount > 0 && !isOnline && (
        <MessageBar
          messageBarType={MessageBarType.warning}
          actions={(
            <div>
              <button
                type="button"
                onClick={handleManualSync}
                disabled={isManualSyncing}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#0078d4',
                  cursor: 'pointer',
                  textDecoration: 'underline',
                  fontSize: '12px',
                }}
              >
                再試行
              </button>
            </div>
          )}
        >
          <Text variant="small">
            {queueCount}
            件のデータが未送信です。ネットワーク接続を確認してください。
          </Text>
        </MessageBar>
      )}

      {/* 状態表示 */}
      <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 8 }}>
        {getStatusInfo.icon ? (
          <Icon
            iconName={getStatusInfo.icon}
            style={{ color: getStatusInfo.color, fontSize: 16 }}
          />
        ) : (
          <Spinner size={1} />
        )}

        <Stack tokens={{ childrenGap: 2 }}>
          <Text variant="medium" style={{ color: getStatusInfo.color, fontWeight: 600 }}>
            {getStatusInfo.text}
          </Text>
          <Text variant="small" style={{ color: '#666' }}>
            {getStatusInfo.description}
          </Text>
        </Stack>

        {/* 手動同期ボタン */}
        {isOnline && !isSyncing && !isTransactionPending && onForcSync && (
          <button
            type="button"
            onClick={handleManualSync}
            disabled={isManualSyncing}
            style={{
              background: 'none',
              border: '1px solid #0078d4',
              borderRadius: '2px',
              color: '#0078d4',
              cursor: 'pointer',
              padding: '4px 8px',
              fontSize: '12px',
              marginLeft: 'auto',
            }}
            title="手動で同期を実行"
          >
            {isManualSyncing ? '同期中...' : '今すぐ同期'}
          </button>
        )}
      </Stack>
    </Stack>
  );
};

TeamsChatsStatusIndicator.defaultProps = {
  /** エラーをクリアする関数 */
  onClearError: undefined,
  /** 手動同期を実行する関数 */
  onForcSync: undefined,
  /** コンパクト表示モード */
  compact: false,
};

export default TeamsChatsStatusIndicator;
